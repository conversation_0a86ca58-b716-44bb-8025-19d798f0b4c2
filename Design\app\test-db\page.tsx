'use client'

import { useState } from 'react'
import { testDatabaseConnection, runComprehensiveTest } from '@/lib/test-database'

export default function TestDatabasePage() {
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<any>(null)

  const handleBasicTest = async () => {
    setIsLoading(true)
    setResults(null)
    
    try {
      const result = await testDatabaseConnection()
      setResults(result)
    } catch (error) {
      setResults({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleComprehensiveTest = async () => {
    setIsLoading(true)
    setResults(null)
    
    try {
      const result = await runComprehensiveTest()
      setResults(result)
    } catch (error) {
      setResults({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow-lg rounded-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            🗄️ Database Connection Test
          </h1>
          
          <div className="space-y-6">
            <div className="text-center">
              <p className="text-gray-600 mb-6">
                Test your Supabase database connection and verify all configurations are working properly.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={handleBasicTest}
                  disabled={isLoading}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? '⏳ Testing...' : '🔍 Basic Connection Test'}
                </button>
                
                <button
                  onClick={handleComprehensiveTest}
                  disabled={isLoading}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? '⏳ Testing...' : '🚀 Comprehensive Test'}
                </button>
              </div>
            </div>

            {results && (
              <div className="mt-8">
                <div className={`p-6 rounded-lg ${results.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                  <div className="flex items-center mb-4">
                    <span className="text-2xl mr-3">
                      {results.success ? '✅' : '❌'}
                    </span>
                    <h2 className={`text-xl font-semibold ${results.success ? 'text-green-800' : 'text-red-800'}`}>
                      {results.success ? 'Test Passed!' : 'Test Failed'}
                    </h2>
                  </div>
                  
                  {results.success && results.data && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                      <div className="bg-white p-4 rounded-lg shadow">
                        <div className="text-2xl font-bold text-blue-600">{results.data.users}</div>
                        <div className="text-sm text-gray-600">Users</div>
                      </div>
                      <div className="bg-white p-4 rounded-lg shadow">
                        <div className="text-2xl font-bold text-green-600">{results.data.subscriptions}</div>
                        <div className="text-sm text-gray-600">Subscriptions</div>
                      </div>
                      <div className="bg-white p-4 rounded-lg shadow">
                        <div className="text-2xl font-bold text-purple-600">{results.data.products}</div>
                        <div className="text-sm text-gray-600">Products</div>
                      </div>
                      <div className="bg-white p-4 rounded-lg shadow">
                        <div className="text-2xl font-bold text-orange-600">{results.data.sales}</div>
                        <div className="text-sm text-gray-600">Sales</div>
                      </div>
                    </div>
                  )}
                  
                  {results.success && results.data?.memberStats && (
                    <div className="mt-6 p-4 bg-white rounded-lg shadow">
                      <h3 className="font-semibold text-gray-800 mb-2">Member Statistics</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                        <div>Total Members: <span className="font-semibold">{results.data.memberStats.total_members}</span></div>
                        <div>Active: <span className="font-semibold text-green-600">{results.data.memberStats.active_subscriptions}</span></div>
                        <div>Expiring: <span className="font-semibold text-yellow-600">{results.data.memberStats.expiring_subscriptions}</span></div>
                        <div>Expired: <span className="font-semibold text-red-600">{results.data.memberStats.expired_subscriptions}</span></div>
                        <div>Male: <span className="font-semibold">{results.data.memberStats.male_members}</span></div>
                        <div>Female: <span className="font-semibold">{results.data.memberStats.female_members}</span></div>
                      </div>
                    </div>
                  )}
                  
                  {results.success && results.data?.revenueStats && (
                    <div className="mt-4 p-4 bg-white rounded-lg shadow">
                      <h3 className="font-semibold text-gray-800 mb-2">Revenue Statistics (Last 30 Days)</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>Total Revenue: <span className="font-semibold text-green-600">{results.data.revenueStats.total_revenue} DZD</span></div>
                        <div>Subscription Revenue: <span className="font-semibold">{results.data.revenueStats.subscription_revenue} DZD</span></div>
                        <div>Product Revenue: <span className="font-semibold">{results.data.revenueStats.product_revenue} DZD</span></div>
                        <div>Cash Payments: <span className="font-semibold">{results.data.revenueStats.cash_payments} DZD</span></div>
                      </div>
                    </div>
                  )}
                  
                  {results.error && (
                    <div className="mt-4 p-4 bg-red-100 rounded-lg">
                      <h3 className="font-semibold text-red-800 mb-2">Error Details:</h3>
                      <pre className="text-sm text-red-700 whitespace-pre-wrap">{results.error}</pre>
                    </div>
                  )}
                  
                  {results.message && (
                    <div className="mt-4 p-4 bg-blue-100 rounded-lg">
                      <p className="text-blue-800">{results.message}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
