'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers'
import { formatCurrency } from '@/lib/utils'
import { supabase } from '@/lib/supabase'
import { useToast } from '@/hooks/use-toast'
import { ScannerModal } from '@/components/pos/scanner-modal'
import { MemberSelectionModal } from '@/components/pos/member-selection-modal'
import { TransactionHistory } from '@/components/pos/transaction-history'
import { initializeDemoData, resetDemoData } from '@/utils/demo-data'
import { InventoryStorage, ExtendedProduct } from '@/lib/inventory-storage'
import { MemberCreditStorage } from '@/lib/member-credit-storage'
import {
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  CreditCard,
  Banknote,
  Search,
  Package,
  Receipt,
  X,
  Qr<PERSON>ode,
  ScanBarcode,
  User,
  History,
  AlertTriangle,
} from 'lucide-react'

interface Product {
  id: string
  name: string
  category: string
  price_dzd: number
  stock: number
  image_url?: string
  barcode?: string
  qr_code?: string
  expiry_date?: string
}

interface CartItem extends Product {
  quantity: number
}

interface Member {
  id: string
  full_name: string
  phone: string
  email?: string
  gender: 'male' | 'female'
  age: number
  situation: string
  credit_limit?: number
  credit_balance?: number
}

interface Transaction {
  id: string
  date: string
  customer_name: string
  customer_type: 'member' | 'guest'
  payment_type: 'cash' | 'credit'
  items: Array<{
    name: string
    quantity: number
    price: number
  }>
  total_amount: number
  created_at: string
}

const categories = ['All', 'Supplements', 'Beverages', 'Snacks', 'Accessories', 'Equipment']

export default function POSPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [cart, setCart] = useState<CartItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)
  const [showPayment, setShowPayment] = useState(false)
  const [paymentType, setPaymentType] = useState<'cash' | 'credit'>('cash')
  const [processing, setProcessing] = useState(false)
  const [showScanner, setShowScanner] = useState(false)
  const [showMemberSelection, setShowMemberSelection] = useState(false)
  const [showTransactionHistory, setShowTransactionHistory] = useState(false)
  const [selectedMember, setSelectedMember] = useState<Member | null>(null)
  const [scanType, setScanType] = useState<'qr' | 'barcode' | 'both'>('both')
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    // Initialize demo data on first load
    initializeDemoData()
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      // Load from localStorage first (for development phase)
      const storedProducts = InventoryStorage.getFromStorage<ExtendedProduct>('gym_products')
      if (storedProducts.length > 0) {
        const productData = storedProducts.filter((p: ExtendedProduct) => p.stock > 0)
        setProducts(productData.map(p => ({
          id: p.id,
          name: p.name,
          category: p.category,
          price_dzd: p.price_dzd,
          stock: p.stock,
          image_url: p.image_url,
          barcode: p.barcode,
          qr_code: p.qr_code,
          expiry_date: p.expiry_date
        })))

        // Update categories based on loaded products
        const uniqueCategories = ['All', ...Array.from(new Set(productData.map(p => p.category)))]
        setCategories(uniqueCategories)
      } else {
        // Initialize with sample products if none exist
        InventoryStorage.initializeData()
        // Retry loading after initialization
        const initializedProducts = InventoryStorage.getFromStorage<ExtendedProduct>('gym_products')
        if (initializedProducts.length > 0) {
          const productData = initializedProducts.filter((p: ExtendedProduct) => p.stock > 0)
          setProducts(productData.map(p => ({
            id: p.id,
            name: p.name,
            category: p.category,
            price_dzd: p.price_dzd,
            stock: p.stock,
            image_url: p.image_url,
            barcode: p.barcode,
            qr_code: p.qr_code,
            expiry_date: p.expiry_date
          })))

          // Update categories based on loaded products
          const uniqueCategories = ['All', ...Array.from(new Set(productData.map(p => p.category)))]
          setCategories(uniqueCategories)
        }
      }

      // Optional: Try to sync with Supabase in the background (for future use)
      try {
        const { data, error } = await supabase
          .from('products')
          .select('*')
          .gt('stock', 0)
          .order('name')

        if (!error && data && data.length > 0) {
          // If Supabase has data, we could sync it here in the future
          console.log('Supabase products available for future sync:', data.length)
        }
      } catch (supabaseError) {
        // Supabase not available, continue with localStorage
        console.log('Supabase not available, using localStorage')
      }
    } catch (error) {
      console.error('Error loading products:', error)
      toast({
        title: 'Error',
        description: 'Failed to load products',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // Get product sales count for sorting
  const getProductSalesCount = (productId: string): number => {
    try {
      const transactions = JSON.parse(localStorage.getItem('gym_transactions') || '[]')
      let salesCount = 0

      transactions.forEach((transaction: Transaction) => {
        transaction.items.forEach(item => {
          if (item.name === products.find(p => p.id === productId)?.name) {
            salesCount += item.quantity
          }
        })
      })

      return salesCount
    } catch (error) {
      return 0
    }
  }

  const filteredProducts = products
    .filter(product => {
      const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase())
      return matchesCategory && matchesSearch
    })
    .sort((a, b) => {
      // Sort by sales count (popular products first), then by name
      const aSales = getProductSalesCount(a.id)
      const bSales = getProductSalesCount(b.id)

      if (aSales !== bSales) {
        return bSales - aSales // Higher sales first
      }

      return a.name.localeCompare(b.name) // Alphabetical as secondary sort
    })

  const addToCart = (product: Product) => {
    setCart(prev => {
      const existingItem = prev.find(item => item.id === product.id)
      if (existingItem) {
        if (existingItem.quantity < product.stock) {
          return prev.map(item =>
            item.id === product.id
              ? { ...item, quantity: item.quantity + 1 }
              : item
          )
        } else {
          toast({
            title: 'Stock Limit',
            description: `Only ${product.stock} items available`,
            variant: 'destructive',
          })
          return prev
        }
      } else {
        return [...prev, { ...product, quantity: 1 }]
      }
    })
  }

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity === 0) {
      removeFromCart(productId)
      return
    }

    const product = products.find(p => p.id === productId)
    if (product && newQuantity > product.stock) {
      toast({
        title: 'Stock Limit',
        description: `Only ${product.stock} items available`,
        variant: 'destructive',
      })
      return
    }

    setCart(prev =>
      prev.map(item =>
        item.id === productId
          ? { ...item, quantity: newQuantity }
          : item
      )
    )
  }

  const removeFromCart = (productId: string) => {
    setCart(prev => prev.filter(item => item.id !== productId))
  }

  const clearCart = () => {
    setCart([])
  }

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + (item.price_dzd * item.quantity), 0)
  }

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0)
  }

  const handleScanSuccess = (result: string) => {
    // Try to find product by barcode or QR code
    const product = products.find(p =>
      p.barcode === result ||
      p.qr_code === result ||
      p.id === result
    )

    if (product) {
      addToCart(product)
      toast({
        title: t('scan_result'),
        description: `${product.name} added to cart`,
      })
    } else {
      toast({
        title: t('invalid_code'),
        description: t('code_not_found'),
        variant: 'destructive',
      })
    }
  }

  const handleMemberSelection = (member: Member) => {
    // Validate credit limit for credit purchases
    if (paymentType === 'credit' && !validateCreditPurchase(member, getTotalAmount())) {
      return // Don't proceed if credit validation fails
    }

    setSelectedMember(member)
    setShowMemberSelection(false)
    setShowPayment(true)
  }

  const handlePaymentTypeSelection = (type: 'cash' | 'credit') => {
    setPaymentType(type)

    if (type === 'credit') {
      setShowMemberSelection(true)
    } else {
      setSelectedMember(null)
      setShowPayment(true)
    }
  }

  const validateCreditPurchase = (member: Member, totalAmount: number): boolean => {
    const creditCheck = MemberCreditStorage.canMemberPurchaseOnCredit(member.id, totalAmount)

    if (!creditCheck.canPurchase) {
      if (creditCheck.creditLimit <= 0) {
        toast({
          title: 'Credit Not Available',
          description: `${member.full_name} does not have a credit limit set.`,
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Credit Limit Exceeded',
          description: `Purchase amount (${formatCurrency(totalAmount)}) would exceed ${member.full_name}'s available credit (${formatCurrency(creditCheck.availableCredit)}).`,
          variant: 'destructive',
        })
      }
      return false
    }

    // Warning if approaching credit limit
    if (creditCheck.availableCredit - totalAmount < creditCheck.creditLimit * 0.1) {
      toast({
        title: 'Credit Limit Warning',
        description: `${member.full_name} will have only ${formatCurrency(creditCheck.availableCredit - totalAmount)} credit remaining after this purchase.`,
        variant: 'default',
      })
    }

    return true
  }

  const isProductExpired = (product: Product) => {
    if (!product.expiry_date) return false
    return new Date(product.expiry_date) < new Date()
  }

  const isProductExpiringSoon = (product: Product) => {
    if (!product.expiry_date) return false
    const expiryDate = new Date(product.expiry_date)
    const today = new Date()
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0
  }

  const processSale = async () => {
    if (cart.length === 0) return

    setProcessing(true)
    try {
      // Create transaction record
      const transaction: Transaction = {
        id: `TXN-${Date.now()}`,
        date: new Date().toISOString().split('T')[0],
        customer_name: selectedMember ? selectedMember.full_name : t('guest_customer'),
        customer_type: selectedMember && selectedMember.id !== 'guest' ? 'member' : 'guest',
        payment_type: paymentType,
        items: cart.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price_dzd
        })),
        total_amount: getTotalAmount(),
        created_at: new Date().toISOString()
      }

      // Save transaction to localStorage
      const existingTransactions = JSON.parse(localStorage.getItem('gym_transactions') || '[]')
      existingTransactions.push(transaction)
      localStorage.setItem('gym_transactions', JSON.stringify(existingTransactions))

      // Handle credit transaction if payment type is credit
      if (paymentType === 'credit' && selectedMember && selectedMember.id !== 'guest') {
        const success = MemberCreditStorage.updateMemberCreditBalance(
          selectedMember.id,
          selectedMember.full_name,
          getTotalAmount(),
          'purchase',
          transaction.id,
          `POS Purchase - ${cart.length} items`
        )

        if (!success) {
          throw new Error('Failed to process credit transaction')
        }

        // Sync member credit data
        MemberCreditStorage.syncMemberCreditData()
      }

      // Update product stock in localStorage using InventoryStorage
      const storedProducts = InventoryStorage.getFromStorage<ExtendedProduct>('gym_products')
      const updatedProducts = storedProducts.map(product => {
        const cartItem = cart.find(item => item.id === product.id)
        if (cartItem) {
          // Create stock movement record
          InventoryStorage.addStockMovement({
            product_id: product.id,
            product_name: product.name,
            movement_type: 'sale',
            quantity_change: -cartItem.quantity,
            previous_stock: product.stock,
            new_stock: product.stock - cartItem.quantity,
            reference_id: transaction.id,
            reference_type: 'sale',
            notes: `Sale transaction - ${cartItem.quantity} units sold`,
          })

          return { ...product, stock: product.stock - cartItem.quantity, updated_at: new Date().toISOString() }
        }
        return product
      })
      InventoryStorage.saveToStorage('gym_products', updatedProducts)

      // Try to sync with Supabase if available
      try {
        const { data: sale, error: saleError } = await supabase
          .from('sales')
          .insert({
            total_price_dzd: getTotalAmount(),
            payment_type: paymentType,
            customer_name: transaction.customer_name,
            customer_type: transaction.customer_type,
          })
          .select()
          .single()

        if (!saleError && sale) {
          // Create sale items
          const saleItems = cart.map(item => ({
            sale_id: sale.id,
            product_id: item.id,
            quantity: item.quantity,
            unit_price_dzd: item.price_dzd,
          }))

          await supabase.from('sales_items').insert(saleItems)

          // Update product stock in Supabase
          for (const item of cart) {
            await supabase
              .from('products')
              .update({ stock: item.stock - item.quantity })
              .eq('id', item.id)
          }
        }
      } catch (supabaseError) {
        console.log('Supabase sync failed, continuing with local storage')
      }

      toast({
        title: t('sale_completed'),
        description: `${t('sale_completed')}: ${formatCurrency(getTotalAmount())}`,
      })

      // Reset state
      clearCart()
      setShowPayment(false)
      setSelectedMember(null)
      fetchProducts() // Refresh products to update stock
    } catch (error) {
      toast({
        title: t('sale_failed'),
        description: t('sale_failed'),
        variant: 'destructive',
      })
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <MainLayout title={t('pos')}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout title={t('pos')}>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]">
        {/* Products Section */}
        <div className="lg:col-span-2 space-y-4">
          {/* Search and Categories */}
          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* Search and Scanner Buttons */}
                <div className="flex space-x-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder={t('search_products')}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      setScanType('qr')
                      setShowScanner(true)
                    }}
                    title={t('scan_qr_code')}
                  >
                    <QrCode className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      setScanType('barcode')
                      setShowScanner(true)
                    }}
                    title={t('scan_barcode')}
                  >
                    <ScanBarcode className="w-4 h-4" />
                  </Button>
                </div>

                {/* Categories */}
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? 'gym' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                      className="text-xs"
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Products Grid */}
          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 overflow-auto max-h-[calc(100vh-20rem)]">
                {filteredProducts.map((product, index) => {
                  const salesCount = getProductSalesCount(product.id)
              const expired = isProductExpired(product)
              const expiringSoon = isProductExpiringSoon(product)

              return (
                <Card
                  key={product.id}
                  className={`glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer relative ${
                    expired ? 'opacity-50 border-red-500' : expiringSoon ? 'border-yellow-500' : ''
                  } ${salesCount > 0 && index < 3 ? 'ring-2 ring-yellow-400/50' : ''}`}
                  onClick={() => !expired && addToCart(product)}
                >
                  <CardContent className="p-3">
                    <div className="space-y-2">
                      {/* Popular Product Badge */}
                      {salesCount > 0 && index < 3 && (
                        <div className="absolute -top-2 -right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-bold shadow-lg z-10">
                          🔥 {salesCount}
                        </div>
                      )}
                      {/* Expiry Warning */}
                      {(expired || expiringSoon) && (
                        <div className={`flex items-center space-x-1 text-xs ${
                          expired ? 'text-red-500' : 'text-yellow-500'
                        }`}>
                          <AlertTriangle className="w-3 h-3" />
                          <span>{expired ? t('product_expired') : t('expires_soon')}</span>
                        </div>
                      )}

                      <div className="w-full h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center overflow-hidden mb-2">
                        {product.image_url ? (
                          <img
                            src={product.image_url}
                            alt={product.name}
                            className="w-full h-full object-cover rounded-lg"
                            onError={(e) => {
                              // Fallback to placeholder if image fails to load
                              const target = e.target as HTMLImageElement
                              target.style.display = 'none'
                              target.nextElementSibling?.classList.remove('hidden')
                            }}
                          />
                        ) : null}
                        <Package className={`w-6 h-6 text-gray-400 ${product.image_url ? 'hidden' : ''}`} />
                      </div>
                      <div className="min-h-0 flex-1 overflow-hidden">
                        <h3 className="font-medium text-xs text-gray-900 dark:text-white leading-tight mb-1 overflow-hidden text-ellipsis whitespace-nowrap">
                          {product.name}
                        </h3>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mb-1 truncate">
                          {product.category}
                        </p>
                        {product.expiry_date && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mb-1 truncate">
                            Exp: {new Date(product.expiry_date).toLocaleDateString()}
                          </p>
                        )}
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-bold text-red-600 dark:text-red-400 truncate flex-1 mr-1">
                            {formatCurrency(product.price_dzd)}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
                            {product.stock}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Cart Section */}
        <div className="space-y-4">
          <Card className="glass border-white/20">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center space-x-2">
                  <ShoppingCart className="w-5 h-5" />
                  <span>Cart ({getTotalItems()})</span>
                </span>
                {cart.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearCart}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {cart.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <ShoppingCart className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>Cart is empty</p>
                </div>
              ) : (
                <>
                  {/* Cart Items */}
                  <div className="space-y-3 max-h-64 overflow-auto">
                    {cart.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                      >
                        <div className="flex-1">
                          <h4 className="font-medium text-sm text-gray-900 dark:text-white">
                            {item.name}
                          </h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {formatCurrency(item.price_dzd)} each
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <Minus className="w-3 h-3" />
                          </Button>
                          <span className="w-8 text-center text-sm font-medium">
                            {item.quantity}
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-red-500 hover:text-red-700"
                            onClick={() => removeFromCart(item.id)}
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Total */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-lg font-semibold text-gray-900 dark:text-white">
                        Total:
                      </span>
                      <span className="text-xl font-bold text-red-600 dark:text-red-400">
                        {formatCurrency(getTotalAmount())}
                      </span>
                    </div>

                    {/* Selected Member Display */}
                    {selectedMember && (
                      <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <User className="w-4 h-4 text-blue-500" />
                          <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                            {selectedMember.full_name}
                          </span>
                        </div>
                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                          {selectedMember.id === 'guest' ? t('guest_customer') : t('select_member')}
                        </p>
                      </div>
                    )}

                    {/* Payment Buttons */}
                    <div className="space-y-2">
                      <Button
                        variant="gym"
                        className="w-full"
                        onClick={() => handlePaymentTypeSelection('cash')}
                      >
                        <Banknote className="w-4 h-4 mr-2" />
                        {t('pay_cash')}
                      </Button>
                      <Button
                        variant="gym-secondary"
                        className="w-full"
                        onClick={() => handlePaymentTypeSelection('credit')}
                      >
                        <CreditCard className="w-4 h-4 mr-2" />
                        {t('pay_credit')}
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Transaction History Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('transaction_history')}
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTransactionHistory(!showTransactionHistory)}
            >
              <History className="w-4 h-4 mr-2" />
              {showTransactionHistory ? 'Hide' : 'Show'}
            </Button>
          </div>

          {showTransactionHistory && (
            <TransactionHistory />
          )}
        </div>
      </div>

      {/* Payment Confirmation Modal */}
      {showPayment && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Confirm Payment</span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowPayment(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {formatCurrency(getTotalAmount())}
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  {t('payment_method')}: {paymentType === 'cash' ? t('cash') : t('credit')}
                </p>
                {selectedMember && (
                  <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {t('customer')}: {selectedMember.full_name}
                    </p>
                  </div>
                )}
              </div>

              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => setShowPayment(false)}
                  disabled={processing}
                >
                  Cancel
                </Button>
                <Button
                  variant="gym"
                  className="flex-1"
                  onClick={processSale}
                  disabled={processing}
                >
                  {processing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      {t('processing')}
                    </>
                  ) : (
                    <>
                      <Receipt className="w-4 h-4 mr-2" />
                      {t('confirm_sale')}
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Scanner Modal */}
      <ScannerModal
        isOpen={showScanner}
        onClose={() => setShowScanner(false)}
        onScanSuccess={handleScanSuccess}
        scanType={scanType}
      />

      {/* Member Selection Modal */}
      <MemberSelectionModal
        isOpen={showMemberSelection}
        onClose={() => setShowMemberSelection(false)}
        onSelectMember={(member) => {
          handleMemberSelection(member)
          setShowPayment(true)
        }}
      />
    </MainLayout>
  )
}
