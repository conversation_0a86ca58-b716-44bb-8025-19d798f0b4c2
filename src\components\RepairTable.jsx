import React from 'react';
import { useLanguage } from '../LanguageContext';

const RepairTable = ({ 
  savedRepairs,
  repairClientFilter,
  repairDeviceFilter,
  repairStatusFilter,
  repairDateFilter,
  formatPrice,
  printRepairQRCode,
  setSelectedRepairForActions,
  setShowRepairActionsModal,
  t
}) => {
  const { currentLanguage } = useLanguage();

  return (
    <div className="repair-table-section">
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th>{t('qrCode', 'رمز QR')}</th>
              <th>{t('clientInfo', 'معلومات العميل')}</th>
              <th>{t('phoneNumber', 'رقم الهاتف')}</th>
              <th>{t('device', 'الجهاز')}</th>
              <th>{t('problem', 'المشكلة')}</th>
              <th>{t('repairPrice', 'سعر الإصلاح')}</th>
              <th>{t('totalPrice', 'السعر الإجمالي')}</th>
              <th>{t('status', 'الحالة')}</th>
              <th>{t('actions', 'الإجراءات')}</th>
            </tr>
          </thead>
          <tbody>
            {savedRepairs
              .filter(repair => {
                const matchesClient = !repairClientFilter ||
                  repair.clientName.toLowerCase().includes(repairClientFilter.toLowerCase());
                const matchesDevice = !repairDeviceFilter ||
                  repair.deviceName.toLowerCase().includes(repairDeviceFilter.toLowerCase());
                const matchesStatus = !repairStatusFilter || repair.situation === repairStatusFilter;
                const matchesDate = !repairDateFilter || repair.dropOffDate === repairDateFilter;

                return matchesClient && matchesDevice && matchesStatus && matchesDate;
              })
              .sort((a, b) => {
                // Define status priority: en attente client first, in process second, rejected third, terminer last
                const statusPriority = {
                  'Waiting for Client': 1,
                  'In Process': 2,
                  'Failed': 3,
                  'Done': 4
                };
                
                const aPriority = statusPriority[a.situation] || 5;
                const bPriority = statusPriority[b.situation] || 5;
                
                // First sort by status priority
                if (aPriority !== bPriority) {
                  return aPriority - bPriority;
                }
                
                // Then sort by date (newest first within same status)
                return new Date(b.dropOffDate) - new Date(a.dropOffDate);
              })
              .map((repair) => (
                <tr key={repair.id}>
                  <td>
                    <div
                      className="qr-cell"
                      onClick={() => printRepairQRCode(repair)}
                      title={t('clickToPrintQR', 'اضغط لطباعة رمز QR')}
                    >
                      <div className="qr-icon">📱</div>
                      <div className="repair-id">{repair.id}</div>
                    </div>
                  </td>
                  <td>
                    <div className="client-info">
                      <div className="client-name">{repair.clientName}</div>
                    </div>
                  </td>
                  <td>
                    <div className="phone-info">
                      <div className="phone-number">{repair.clientPhone || t('noPhone', 'لا يوجد هاتف')}</div>
                    </div>
                  </td>
                  <td>
                    <div className="device-info">
                      <div className="device-name">{repair.deviceName}</div>
                    </div>
                  </td>
                  <td>
                    <div className="problem-info">
                      <div className="problem-text">{repair.problem}</div>
                    </div>
                  </td>
                  <td>
                    <div className="price-cell">
                      <div className="price-amount">
                        {formatPrice(parseFloat(repair.repairPrice) || 0)}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="price-cell">
                      <div className="total-price-amount">
                        {formatPrice((parseFloat(repair.repairPrice) || 0) - (parseFloat(repair.partsPrice) || 0))}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="status-cell">
                      <span className={`status-badge status-${repair.situation.toLowerCase().replace(/\s+/g, '-')}`}>
                        {t(repair.situation.toLowerCase().replace(/\s+/g, ''), repair.situation)}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="repair-actions-cell">
                      <button
                        className="repair-actions-btn modern-actions-btn"
                        onClick={() => {
                          setSelectedRepairForActions(repair);
                          setShowRepairActionsModal(true);
                        }}
                        title={t('actions', 'الإجراءات')}
                      >
                        <span className="actions-icon">⋮</span>
                        <span className="actions-text">{t('actions', 'الإجراءات')}</span>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            {savedRepairs.length === 0 && (
              <tr>
                <td colSpan="9" style={{ textAlign: 'center', padding: '40px' }}>
                  <div className="no-data-message">
                    <div className="no-data-icon">🛠️</div>
                    <h3>{t('noRepairsFound', 'لا توجد إصلاحات')}</h3>
                    <p>{t('addFirstRepair', 'أضف أول إصلاح لبدء العمل')}</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default RepairTable;
