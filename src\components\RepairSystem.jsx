import React, { useState, useEffect } from 'react';
import { useLanguage } from '../LanguageContext';

const RepairSystem = ({ 
  savedRepairs, 
  setSavedRepairs, 
  formatPrice, 
  showToast, 
  currentUser,
  printRepairQRCode,
  printRepairBonPour,
  t
}) => {
  const { currentLanguage } = useLanguage();
  
  // Repair modal states
  const [showNewRepairModal, setShowNewRepairModal] = useState(false);
  const [showRepairCompletionModal, setShowRepairCompletionModal] = useState(false);
  const [showClientRecoveryModal, setShowClientRecoveryModal] = useState(false);
  const [showRepairInfoModal, setShowRepairInfoModal] = useState(false);
  const [showEditRepairModal, setShowEditRepairModal] = useState(false);
  const [showQRScannerModal, setShowQRScannerModal] = useState(false);
  const [showAdminPasscodeModal, setShowAdminPasscodeModal] = useState(false);
  const [showRepairActionsModal, setShowRepairActionsModal] = useState(false);
  const [selectedRepairForActions, setSelectedRepairForActions] = useState(null);

  // Repair form data
  const [newRepair, setNewRepair] = useState({
    id: '',
    clientName: '',
    clientPhone: '',
    deviceName: '',
    deviceType: 'Smartphone',
    problem: '',
    problemDescription: '',
    repairPrice: 0,
    partsPrice: 0,
    dropOffDate: new Date().toISOString().split('T')[0],
    dropOffTime: new Date().toTimeString().slice(0, 5),
    paymentStatus: 'Unpaid',
    partialPayment: 0,
    barcode: '',
    situation: 'In Process',
    remarks: '',
    createdAt: new Date().toISOString()
  });

  // Repair filters
  const [repairClientFilter, setRepairClientFilter] = useState('');
  const [repairDeviceFilter, setRepairDeviceFilter] = useState('');
  const [repairStatusFilter, setRepairStatusFilter] = useState('');
  const [repairDateFilter, setRepairDateFilter] = useState('');

  // Generate next repair ID
  const generateNextRepairId = () => {
    const currentDate = new Date();
    const year = String(currentDate.getFullYear()).slice(-2);
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const day = String(currentDate.getDate()).padStart(2, '0');

    // Find highest existing repair number for today
    const todayPrefix = `REP${year}${month}${day}`;
    const todayRepairs = savedRepairs.filter(repair =>
      repair.id && repair.id.startsWith(todayPrefix)
    );

    let nextNumber = 1;
    if (todayRepairs.length > 0) {
      const numbers = todayRepairs.map(repair => {
        const match = repair.id.match(/REP\d{6}(\d{3})$/);
        return match ? parseInt(match[1]) : 0;
      });
      nextNumber = Math.max(...numbers) + 1;
    }

    return `${todayPrefix}${String(nextNumber).padStart(3, '0')}`;
  };

  // Generate repair barcode
  const generateRepairBarcode = (repairId) => {
    return `BR${repairId}${Date.now().toString().slice(-4)}`;
  };

  // Reset new repair form
  const resetNewRepairForm = () => {
    const newId = generateNextRepairId();
    const newBarcode = generateRepairBarcode(newId);

    setNewRepair({
      id: newId,
      clientName: '',
      clientPhone: '',
      deviceName: '',
      deviceType: 'Smartphone',
      problem: '',
      problemDescription: '',
      repairPrice: 0,
      partsPrice: 0,
      dropOffDate: new Date().toISOString().split('T')[0],
      dropOffTime: new Date().toTimeString().slice(0, 5),
      paymentStatus: 'Unpaid',
      partialPayment: 0,
      barcode: newBarcode,
      situation: 'In Process',
      remarks: '',
      createdAt: new Date().toISOString()
    });
  };

  // Initialize repair form on component mount
  useEffect(() => {
    resetNewRepairForm();
  }, []);

  // Save repairs to localStorage
  const saveRepairs = (repairs) => {
    setSavedRepairs(repairs);
    localStorage.setItem('icaldz-repairs', JSON.stringify(repairs));
  };

  // Add new repair
  const addRepair = () => {
    try {
      if (!newRepair.clientName.trim() || !newRepair.deviceName.trim() || !newRepair.problem.trim()) {
        showToast(`❌ ${t('fillRequiredFields', 'يرجى ملء جميع الحقول المطلوبة')}`, 'error', 3000);
        return;
      }

      const repairToAdd = {
        ...newRepair,
        id: newRepair.id || generateNextRepairId(),
        barcode: newRepair.barcode || generateRepairBarcode(newRepair.id),
        createdAt: new Date().toISOString()
      };

      const updatedRepairs = [...savedRepairs, repairToAdd];
      saveRepairs(updatedRepairs);

      showToast(`✅ ${t('repairAdded', 'تم إضافة الإصلاح بنجاح')} - ${repairToAdd.id}`, 'success', 3000);
      
      setShowNewRepairModal(false);
      resetNewRepairForm();
    } catch (error) {
      console.error('Error adding repair:', error);
      showToast(`❌ ${t('errorAddingRepair', 'خطأ في إضافة الإصلاح')}`, 'error', 3000);
    }
  };

  // Problem types for dropdown
  const problemTypes = [
    'LCD مكسور',
    'شاشة لا تعمل',
    'مشكلة في الشحن',
    'مشكلة في البطارية',
    'مشكلة في الصوت',
    'مشكلة في الكاميرا',
    'مشكلة في الواي فاي',
    'مشكلة في البلوتوث',
    'مشكلة في الذاكرة',
    'فيروسات',
    'بطء في النظام',
    'مشكلة في التطبيقات',
    'مشكلة في النظام',
    'أخرى'
  ];

  const [customProblemTypes, setCustomProblemTypes] = useState([]);

  return {
    // States
    showNewRepairModal,
    setShowNewRepairModal,
    showRepairCompletionModal,
    setShowRepairCompletionModal,
    showClientRecoveryModal,
    setShowClientRecoveryModal,
    showRepairInfoModal,
    setShowRepairInfoModal,
    showEditRepairModal,
    setShowEditRepairModal,
    showQRScannerModal,
    setShowQRScannerModal,
    showAdminPasscodeModal,
    setShowAdminPasscodeModal,
    showRepairActionsModal,
    setShowRepairActionsModal,
    selectedRepairForActions,
    setSelectedRepairForActions,
    newRepair,
    setNewRepair,
    repairClientFilter,
    setRepairClientFilter,
    repairDeviceFilter,
    setRepairDeviceFilter,
    repairStatusFilter,
    setRepairStatusFilter,
    repairDateFilter,
    setRepairDateFilter,
    problemTypes,
    customProblemTypes,
    setCustomProblemTypes,
    
    // Functions
    generateNextRepairId,
    generateRepairBarcode,
    resetNewRepairForm,
    saveRepairs,
    addRepair
  };
};

export default RepairSystem;
