'use client'

import { useState } from 'react'
import { userOperations } from '@/lib/database'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestConnectionPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const testConnection = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('Testing database connection...')
      const users = await userOperations.getAll()
      console.log('Users fetched:', users)
      setResult({
        success: true,
        userCount: users.length,
        users: users.slice(0, 3) // Show first 3 users
      })
    } catch (err: any) {
      console.error('Database connection error:', err)
      setError(err.message || 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Database Connection Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testConnection} 
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Testing...' : 'Test Database Connection'}
            </Button>

            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h3 className="font-semibold text-red-800">Error:</h3>
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {result && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-semibold text-green-800">Success!</h3>
                <p className="text-green-700">Found {result.userCount} users in database</p>
                {result.users.length > 0 && (
                  <div className="mt-2">
                    <h4 className="font-medium">Sample users:</h4>
                    <ul className="list-disc list-inside">
                      {result.users.map((user: any) => (
                        <li key={user.id} className="text-sm">
                          {user.full_name} - {user.phone}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
