'use client'

import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers'
import { formatCurrency } from '@/lib/utils'
import {
  Users,
  DollarSign,
  Calendar,
  ShoppingCart,
  TrendingUp,
  TrendingDown,
  Plus,
  AlertTriangle,
  Clock,
  Activity,
} from 'lucide-react'

// Mock data - replace with real data from Supabase
const stats = {
  totalMembers: 245,
  activeMembers: 198,
  dailyRevenue: 45600,
  monthlyRevenue: 1250000,
  expiringSubscriptions: 12,
  todaySales: 8,
  lowStockItems: 3,
  todayClasses: 6,
}

const recentActivities = [
  {
    id: 1,
    type: 'member',
    message: 'New member <PERSON> registered',
    time: '2 minutes ago',
    icon: Users,
    color: 'text-green-500',
  },
  {
    id: 2,
    type: 'sale',
    message: 'Sale completed: Whey Protein - 8,500 DA',
    time: '15 minutes ago',
    icon: ShoppingCart,
    color: 'text-blue-500',
  },
  {
    id: 3,
    type: 'subscription',
    message: 'Subscription renewed: <PERSON><PERSON> (Boxing)',
    time: '1 hour ago',
    icon: Calendar,
    color: 'text-purple-500',
  },
  {
    id: 4,
    type: 'alert',
    message: 'Low stock alert: Energy Drinks (5 remaining)',
    time: '2 hours ago',
    icon: AlertTriangle,
    color: 'text-orange-500',
  },
]

const quickActions = [
  {
    title: 'Add New Member',
    description: 'Register a new gym member',
    href: '/members/new',
    icon: Users,
    color: 'bg-green-500',
  },
  {
    title: 'Open POS',
    description: 'Start a new sale transaction',
    href: '/pos',
    icon: ShoppingCart,
    color: 'bg-blue-500',
  },
  {
    title: 'Renew Subscription',
    description: 'Extend member subscription',
    href: '/members?action=renew',
    icon: Calendar,
    color: 'bg-purple-500',
  },
  {
    title: 'Add Product',
    description: 'Add new inventory item',
    href: '/inventory/products/new',
    icon: Plus,
    color: 'bg-orange-500',
  },
]

export default function DashboardPage() {
  const { t } = useLanguage()

  return (
    <MainLayout title={t('dashboard')}>
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Members */}
          <Card className="glass border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-bold text-gray-600 dark:text-gray-400">
                Total Members
              </CardTitle>
              <Users className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-black text-gray-900 dark:text-white">
                {stats.totalMembers}
              </div>
              <p className="text-xs text-green-600 dark:text-green-400 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +12% from last month
              </p>
            </CardContent>
          </Card>

          {/* Daily Revenue */}
          <Card className="glass border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-bold text-gray-600 dark:text-gray-400">
                Daily Revenue
              </CardTitle>
              <DollarSign className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-black text-gray-900 dark:text-white">
                {formatCurrency(stats.dailyRevenue)}
              </div>
              <p className="text-xs text-blue-600 dark:text-blue-400 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +8% from yesterday
              </p>
            </CardContent>
          </Card>

          {/* Expiring Subscriptions */}
          <Card className="glass border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-bold text-gray-600 dark:text-gray-400">
                Expiring Soon
              </CardTitle>
              <Clock className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-black text-gray-900 dark:text-white">
                {stats.expiringSubscriptions}
              </div>
              <p className="text-xs text-orange-600 dark:text-orange-400 flex items-center mt-1">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Next 7 days
              </p>
            </CardContent>
          </Card>

          {/* Today's Sales */}
          <Card className="glass border-white/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-bold text-gray-600 dark:text-gray-400">
                Today's Sales
              </CardTitle>
              <Activity className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-black text-gray-900 dark:text-white">
                {stats.todaySales}
              </div>
              <p className="text-xs text-purple-600 dark:text-purple-400 flex items-center mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                +3 from yesterday
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="text-2xl font-black text-gray-900 dark:text-white tracking-tight">
              Quick Actions
            </CardTitle>
            <CardDescription className="text-base font-semibold text-gray-600 dark:text-gray-400">
              Frequently used actions for faster workflow
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action) => {
                const Icon = action.icon
                return (
                  <Button
                    key={action.title}
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-center space-y-2 hover:shadow-md transition-all duration-200"
                    asChild
                  >
                    <a href={action.href}>
                      <div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center`}>
                        <Icon className="w-5 h-5 text-white" />
                      </div>
                      <div className="text-center">
                        <p className="font-bold text-base text-gray-900 dark:text-white">
                          {action.title}
                        </p>
                        <p className="text-sm font-semibold text-gray-500 dark:text-gray-400">
                          {action.description}
                        </p>
                      </div>
                    </a>
                  </Button>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="text-2xl font-black text-gray-900 dark:text-white tracking-tight">
              Recent Activity
            </CardTitle>
            <CardDescription className="text-base font-semibold text-gray-600 dark:text-gray-400">
              Latest updates and notifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => {
                const Icon = activity.icon
                return (
                  <div
                    key={activity.id}
                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    <div className={`w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center`}>
                      <Icon className={`w-4 h-4 ${activity.color}`} />
                    </div>
                    <div className="flex-1">
                      <p className="text-base font-bold text-gray-900 dark:text-white">
                        {activity.message}
                      </p>
                      <p className="text-sm font-semibold text-gray-500 dark:text-gray-400">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
