import React, { useState } from 'react';
import { useLanguage } from '../LanguageContext';

const SupplierManagement = ({ 
  suppliers,
  setSuppliers,
  savedPurchases,
  formatPrice,
  showToast,
  t
}) => {
  const { currentLanguage } = useLanguage();
  
  // Supplier states
  const [showSupplierModal, setShowSupplierModal] = useState(false);
  const [showSupplierTransactionsModal, setShowSupplierTransactionsModal] = useState(false);
  const [selectedSupplierForTransactions, setSelectedSupplierForTransactions] = useState(null);
  const [newSupplier, setNewSupplier] = useState({
    id: '',
    name: '',
    phone: '',
    address: '',
    email: '',
    notes: '',
    createdAt: new Date().toISOString()
  });

  // Generate next supplier ID
  const generateNextSupplierId = () => {
    const currentDate = new Date();
    const year = String(currentDate.getFullYear()).slice(-2);
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const day = String(currentDate.getDate()).padStart(2, '0');

    const todayPrefix = `SUP${year}${month}${day}`;
    const todaySuppliers = suppliers.filter(supplier =>
      supplier.id && supplier.id.startsWith(todayPrefix)
    );

    let nextNumber = 1;
    if (todaySuppliers.length > 0) {
      const numbers = todaySuppliers.map(supplier => {
        const match = supplier.id.match(/SUP\d{6}(\d{3})$/);
        return match ? parseInt(match[1]) : 0;
      });
      nextNumber = Math.max(...numbers) + 1;
    }

    return `${todayPrefix}${String(nextNumber).padStart(3, '0')}`;
  };

  // Reset supplier form
  const resetSupplierForm = () => {
    setNewSupplier({
      id: generateNextSupplierId(),
      name: '',
      phone: '',
      address: '',
      email: '',
      notes: '',
      createdAt: new Date().toISOString()
    });
  };

  // Add new supplier
  const addSupplier = () => {
    try {
      if (!newSupplier.name.trim()) {
        showToast(`❌ ${t('supplierNameRequired', 'اسم المورد مطلوب')}`, 'error', 3000);
        return;
      }

      const supplierToAdd = {
        ...newSupplier,
        id: newSupplier.id || generateNextSupplierId(),
        createdAt: new Date().toISOString()
      };

      const updatedSuppliers = [...suppliers, supplierToAdd];
      setSuppliers(updatedSuppliers);
      localStorage.setItem('icaldz-suppliers', JSON.stringify(updatedSuppliers));

      showToast(`✅ ${t('supplierAdded', 'تم إضافة المورد بنجاح')} - ${supplierToAdd.name}`, 'success', 3000);
      
      setShowSupplierModal(false);
      resetSupplierForm();
    } catch (error) {
      console.error('Error adding supplier:', error);
      showToast(`❌ ${t('errorAddingSupplier', 'خطأ في إضافة المورد')}`, 'error', 3000);
    }
  };

  // Mark supplier transaction as paid
  const markTransactionPaid = (purchaseId) => {
    // This function will be implemented in the main component
    console.log('Mark transaction paid:', purchaseId);
  };

  // Print supplier receipt
  const printSupplierReceipt = (purchase, type) => {
    // This function will be implemented in the main component
    console.log('Print supplier receipt:', purchase, type);
  };

  // Open supplier transactions modal
  const openSupplierTransactionsModal = (supplier) => {
    setSelectedSupplierForTransactions(supplier);
    setShowSupplierTransactionsModal(true);
  };

  // Close supplier transactions modal
  const closeSupplierTransactionsModal = () => {
    setShowSupplierTransactionsModal(false);
    setSelectedSupplierForTransactions(null);
  };

  // Calculate supplier statistics
  const getSupplierStats = (supplierName) => {
    const supplierPurchases = savedPurchases.filter(p => p.supplierName === supplierName);
    
    return {
      totalPurchases: supplierPurchases.length,
      totalAmount: supplierPurchases.reduce((total, p) => total + (parseFloat(p.finalTotal) || 0), 0),
      paidAmount: supplierPurchases
        .filter(p => p.paymentMethod === 'نقداً')
        .reduce((total, p) => total + (parseFloat(p.finalTotal) || 0), 0),
      creditAmount: supplierPurchases
        .filter(p => p.paymentMethod === 'دين')
        .reduce((total, p) => total + (parseFloat(p.finalTotal) || 0), 0)
    };
  };

  return {
    // States
    showSupplierModal,
    setShowSupplierModal,
    showSupplierTransactionsModal,
    setShowSupplierTransactionsModal,
    selectedSupplierForTransactions,
    setSelectedSupplierForTransactions,
    newSupplier,
    setNewSupplier,
    
    // Functions
    generateNextSupplierId,
    resetSupplierForm,
    addSupplier,
    markTransactionPaid,
    printSupplierReceipt,
    openSupplierTransactionsModal,
    closeSupplierTransactionsModal,
    getSupplierStats
  };
};

export default SupplierManagement;
